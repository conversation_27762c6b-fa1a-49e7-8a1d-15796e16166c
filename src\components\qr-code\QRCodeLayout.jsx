"use client";

import React, { useState } from "react";
import {
  QrCodeIcon,
  DocumentDuplicateIcon,
  ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";
import { toast } from "react-toastify";
import NetworkService from "@/network/service/network_service";
import ApiPath from "@/network/api/api_path";
import useSWR from "swr";

const networkService = new NetworkService();

// SWR fetcher
const fetcher = async (url) => {
  const res = await networkService.get(url);
  if (res.status === "completed") return res.data.data;
  return null;
};

function QRCodeLayout() {
  // SWR cache: only fetch once, no background revalidate
  const { data: qrData } = useSWR(ApiPath.qrCode, fetcher, {
    revalidateOnFocus: false, // tab focus এ refetch হবে না
    revalidateIfStale: false, // stale data থাকলেও refetch হবে না
    dedupingInterval: Infinity, // একবার fetch হয়ে গেলে repeated request block
  });

  const [generatedQR, setGeneratedQR] = useState(null);

  const generateQRCode = () => {
    if (!qrData) return;
    setGeneratedQR({
      data: qrData,
      timestamp: new Date().toISOString(),
    });
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard");
  };

  const downloadSVG = (svgString, filename = "qr-code.svg") => {
    const blob = new Blob([svgString], { type: "image/svg+xml" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          QR Code Generator
        </h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* QR Code Generator Form */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Generate Payment QR Code
            </h2>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Create a QR code for receiving payments.
            </p>
          </div>
          <div className="p-6">
            <form
              className="space-y-4"
              onSubmit={(e) => {
                e.preventDefault();
                generateQRCode();
              }}
            >
              <button
                type="submit"
                className="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <QrCodeIcon className="w-5 h-5 mr-2" />
                Generate QR Code
              </button>
            </form>
          </div>
        </div>

        {/* Generated QR Code Display */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Generated QR Code
            </h2>
          </div>
          <div className="p-6">
            {generatedQR ? (
              <div className="space-y-4">
                <div className="flex justify-center flex-col items-center">
                  <div
                    className="w-64 h-64 bg-gray-100 dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center"
                    dangerouslySetInnerHTML={{ __html: qrData }}
                  />
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    QR Code Generated
                  </p>
                </div>

                {/* QR Code Data */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      QR Code Data:
                    </span>
                    <button
                      onClick={() => copyToClipboard(generatedQR.data)}
                      className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-800"
                    >
                      <DocumentDuplicateIcon className="w-3 h-3 mr-1" />
                      Copy
                    </button>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 break-all font-mono">
                    {generatedQR.data}
                  </p>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => downloadSVG(generatedQR.data)}
                    className="flex-1 inline-flex justify-center items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                  >
                    <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                    Download
                  </button>

                  <button className="flex-1 inline-flex justify-center items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                    Share
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <QrCodeIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  Fill in the form and click "Generate QR Code" to create your
                  payment QR code.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default QRCodeLayout;
