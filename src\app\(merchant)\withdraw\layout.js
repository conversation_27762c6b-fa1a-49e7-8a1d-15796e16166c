"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  ArrowDownOnSquareStackIcon,
  CreditCardIcon,
  ClockIcon
} from "@heroicons/react/24/outline";

const WithdrawLayout = ({ children }) => {
  const pathname = usePathname();

  const isActive = (path) => {
    if (path === "/withdraw/withdraw-account") {
      // Make withdraw-account tab active for both the main page and create page
      return pathname === path || pathname.startsWith("/withdraw/withdraw-account/");
    }
    return pathname === path;
  };

  const tabs = [
    {
      name: "Withdraw Money",
      href: "/withdraw",
      icon: <ArrowDownOnSquareStackIcon className="w-5 h-5" />,
      current: isActive("/withdraw")
    },
    {
      name: "Withdraw Account",
      href: "/withdraw/withdraw-account",
      icon: <CreditCardIcon className="w-5 h-5" />,
      current: isActive("/withdraw/withdraw-account")
    },
    {
      name: "Withdraw History",
      href: "/withdraw/withdraw-history",
      icon: <ClockIcon className="w-5 h-5" />,
      current: isActive("/withdraw/withdraw-history")
    }
  ];

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <Link
              key={tab.name}
              href={tab.href}
              className={`group inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${tab.current
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
            >
              <span className={`mr-2 ${tab.current ? "text-blue-500" : "text-gray-400 group-hover:text-gray-500"}`}>
                {tab.icon}
              </span>
              {tab.name}
            </Link>
          ))}
        </nav>
      </div>

      {/* Page Content */}
      <div>{children}</div>
    </div>
  );
};

export default WithdrawLayout;
