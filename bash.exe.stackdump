Stack trace:
Frame         Function      Args
0007FFFF9F20  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8E20) msys-2.0.dll+0x1FEBA
0007FFFF9F20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA1F8) msys-2.0.dll+0x67F9
0007FFFF9F20  000210046832 (000210285FF9, 0007FFFF9DD8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F20  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9F20  0002100690B4 (0007FFFF9F30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA200  00021006A49D (0007FFFF9F30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB8D0E0000 ntdll.dll
7FFB8B350000 KERNEL32.DLL
7FFB8A840000 KERNELBASE.dll
7FFB8BD70000 USER32.dll
7FFB8A810000 win32u.dll
7FFB8B520000 GDI32.dll
7FFB8A2D0000 gdi32full.dll
7FFB8ADC0000 msvcp_win.dll
000210040000 msys-2.0.dll
7FFB8A410000 ucrtbase.dll
7FFB8C640000 advapi32.dll
7FFB8BF40000 msvcrt.dll
7FFB8C700000 sechost.dll
7FFB8AE70000 RPCRT4.dll
7FFB89800000 CRYPTBASE.DLL
7FFB8A5F0000 bcryptPrimitives.dll
7FFB8B170000 IMM32.DLL
