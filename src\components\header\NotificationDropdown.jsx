"use client";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import { Dropdown } from "../ui/dropdown/Dropdown";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import {
  ArrowDownCircleIcon,
  ArrowUpCircleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import useSWR, { mutate } from "swr";
import { toast } from "react-toastify";

// fetcher function
const fetcher = async (url) => {
  const networkService = new NetworkService();
  const res = await networkService.get(url, { for: "merchant" });
  if (res.status === "completed") return res.data.data.notifications;
  return [];
};

export default function NotificationDropdown() {
  const [isOpen, setIsOpen] = useState(false);
  const [notifying, setNotifying] = useState(true);
  const { data: fetchedNotifications, error } = useSWR(
    ApiPath.getNotifications,
    fetcher,
    {
      revalidateOnFocus: true,
      dedupingInterval: 5000,
    }
  );

  const [notifications, setNotifications] = useState([]);

  // fetchedNotifications set to local state
  useEffect(() => {
    if (fetchedNotifications) setNotifications(fetchedNotifications);
  }, [fetchedNotifications]);

  const toggleDropdown = () => setIsOpen(!isOpen);
  const closeDropdown = () => setIsOpen(false);

  const handleClick = () => {
    toggleDropdown();
    setNotifying(false);
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case "merchant_withdraw_approved":
        return (
          <CheckCircleIcon className="w-10 h-10 text-green-500 bg-green-100 rounded-full p-2" />
        );
      case "merchant_payment":
        return (
          <ArrowDownCircleIcon className="w-10 h-10 text-blue-500 bg-blue-100 rounded-full p-2" />
        );
      case "merchant_withdraw_request":
        return (
          <ArrowUpCircleIcon className="w-10 h-10 text-yellow-500 bg-yellow-100 rounded-full p-2" />
        );
      default:
        return (
          <ExclamationTriangleIcon className="w-10 h-10 text-gray-500 bg-gray-100 rounded-full p-2" />
        );
    }
  };

  const markAllAsRead = async () => {
    if (!notifications || notifications.length === 0) return;

    const networkService = new NetworkService();
    try {
      const res = await networkService.post(ApiPath.markNotificationAsRead, {
        for: "merchant",
      });
      if (res.status === "completed") {
        toast.success(res.data.message);
      }
    } finally {
      mutate(ApiPath.getNotifications);
    }
  };

  if (error) return <div>Notification Failed to load</div>;

  return (
    <div className="relative">
      {/* Notification Button */}
      <button
        className="relative dropdown-toggle flex items-center justify-center text-gray-500 transition-colors bg-white border border-gray-200 rounded-full hover:text-gray-700 h-11 w-11 hover:bg-gray-100 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white"
        onClick={handleClick}
      >
        <span
          className={`absolute right-0 top-0.5 z-10 h-2 w-2 rounded-full bg-orange-400 ${
            !notifying ? "hidden" : "flex"
          }`}
        >
          <span className="absolute inline-flex w-full h-full bg-orange-400 rounded-full opacity-75 animate-ping"></span>
        </span>
        <svg
          className="fill-current"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10.75 2.29248C10.75 1.87827 10.4143 1.54248 10 1.54248C9.58583 1.54248 9.25004 1.87827 9.25004 2.29248V2.83613C6.08266 3.20733 3.62504 5.9004 3.62504 9.16748V14.4591H3.33337C2.91916 14.4591 2.58337 14.7949 2.58337 15.2091C2.58337 15.6234 2.91916 15.9591 3.33337 15.9591H4.37504H15.625H16.6667C17.0809 15.9591 17.4167 15.6234 17.4167 15.2091C17.4167 14.7949 17.0809 14.4591 16.6667 14.4591H16.375V9.16748C16.375 5.9004 13.9174 3.20733 10.75 2.83613V2.29248ZM14.875 14.4591V9.16748C14.875 6.47509 12.6924 4.29248 10 4.29248C7.30765 4.29248 5.12504 6.47509 5.12504 9.16748V14.4591H14.875ZM8.00004 17.7085C8.00004 18.1228 8.33583 18.4585 8.75004 18.4585H11.25C11.6643 18.4585 12 18.1228 12 17.7085C12 17.2943 11.6643 16.9585 11.25 16.9585H8.75004C8.33583 16.9585 8.00004 17.2943 8.00004 17.7085Z"
            fill="currentColor"
          />
        </svg>
      </button>

      {/* Dropdown */}
      <Dropdown
        isOpen={isOpen}
        onClose={closeDropdown}
        className="absolute -right-[240px] mt-[17px] flex h-[480px] w-[350px] flex-col rounded-2xl border border-gray-200 bg-white p-3 shadow-theme-lg dark:border-gray-800 dark:bg-gray-dark sm:w-[361px] lg:right-0"
      >
        <div className="flex items-center justify-between pb-3 mb-3 border-b border-gray-100 dark:border-gray-700">
          <h5 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Notification
          </h5>
          <button
            onClick={toggleDropdown}
            className="text-gray-500 transition dropdown-toggle dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
          >
            <svg
              className="fill-current"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M6.21967 7.28131C5.92678 6.98841 5.92678 6.51354 6.21967 6.22065C6.51256 5.92775 6.98744 5.92775 7.28033 6.22065L11.999 10.9393L16.7176 6.22078C17.0105 5.92789 17.4854 5.92788 17.7782 6.22078C18.0711 6.51367 18.0711 6.98855 17.7782 7.28144L13.0597 12L17.7782 16.7186C18.0711 17.0115 18.0711 17.4863 17.7782 17.7792C17.4854 18.0721 17.0105 18.0721 16.7176 17.7792L11.999 13.0607L7.28033 17.7794C6.98744 18.0722 6.51256 18.0722 6.21967 17.7794C5.92678 17.4865 5.92678 17.0116 6.21967 16.7187L10.9384 12L6.21967 7.28131Z"
                fill="currentColor"
              />
            </svg>
          </button>
        </div>

        <ul className="flex flex-col gap-1 h-auto overflow-y-auto custom-scrollbar">
          {notifications && notifications.length > 0 ? (
            notifications.map((notification, index) => (
              <li key={notification.id || index}>
                <div
                  onClick={closeDropdown}
                  className={`flex gap-3 rounded-lg border-b border-gray-100 p-3 cursor-pointer ${
                    notification.is_read
                      ? "bg-gray-100 dark:bg-gray-800"
                      : "bg-white dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-white/5"
                  }`}
                >
                  <span className="shrink-0">
                    {getNotificationIcon(notification.type)}
                  </span>
                  <span className="block">
                    <span className="mb-1.5 block text-theme-sm text-gray-500 dark:text-gray-400">
                      <span className="font-medium text-gray-800 dark:text-white/90">
                        {notification.title}
                      </span>
                    </span>
                    <span className="text-gray-600 dark:text-gray-300 text-sm">
                      {notification.message}
                    </span>
                    <span className="flex items-center gap-2 mt-1 text-gray-500 text-theme-xs dark:text-gray-400">
                      <span>{notification.type.replace(/_/g, " ")}</span>
                      <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                      <span>{notification.created_at}</span>
                    </span>
                  </span>
                </div>
              </li>
            ))
          ) : (
            <li className="p-4 text-center text-gray-500 dark:text-gray-400">
              No notifications
            </li>
          )}
        </ul>

        <div className="flex gap-2">
          <button
            type="button"
            onClick={markAllAsRead}
            className="block px-2 flex-1 py-2 mt-3 text-sm font-medium text-center text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
          >
            Mark All as Read
          </button>

          <Link
            onClick={closeDropdown}
            href="/notification"
            className="block px-2 flex-1 py-2 mt-3 text-sm font-medium text-center text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
          >
            View All Notifications
          </Link>
        </div>
      </Dropdown>
    </div>
  );
}
