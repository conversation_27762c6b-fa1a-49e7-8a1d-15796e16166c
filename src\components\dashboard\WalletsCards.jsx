"use client";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import axios from "axios";
import Cookies from "js-cookie";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";

const WalletsCards = ({ onWalletCreated }) => {
  // use network
  const networkService = new NetworkService();

  // state
  const [walletsCardsData, setWalletsCardsData] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState("");
  const [currencies, setCurrencies] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch all wallets
  const fetchAllWallets = async () => {
    try {
      const res = await networkService.get(ApiPath.allWallets);
      setWalletsCardsData(res.data.data);
    } finally {
    }
  };

  // Fetch all currencies
  const fetchCurrencies = async () => {
    try {
      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/get-currencies`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setCurrencies(data.data);
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Failed to fetch currencies"
      );
    }
  };

  // Filter only currencies user doesn't already have
  const getAvailableCurrencies = () => {
    const existingCodes = walletsCardsData?.wallets.map((w) => w.code);
    return currencies.filter((c) => !existingCodes.includes(c.code));
  };

  // card style
  const cardStyles = [
    {
      gradient: "from-indigo-600 via-purple-600 to-purple-700",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-blue-600 via-indigo-600 to-purple-600",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-cyan-500 via-blue-500 to-indigo-600",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-violet-600 via-purple-600 to-indigo-700",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
    {
      gradient: "from-emerald-500 via-teal-600 to-cyan-600",
      accent: "bg-white/10 hover:bg-white/20",
      iconBg: "bg-white/15",
    },
  ];

  // Create a wallet
  const createWallet = async () => {
    try {
      setLoading(true);
      const res = await networkService.post(ApiPath.createWallet, {
        currency_id: selectedCurrency,
      });
      if (res.status === "completed") {
        toast.success(res.data.message);
        fetchAllWallets();
        closeModal();
      }
    } finally {
      setLoading(false);
    }
  };

  const openModal = () => {
    if (getAvailableCurrencies().length === 0) {
      toast.info("You already have wallets for all available currencies");
      return;
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedCurrency("");
  };

  useEffect(() => {
    fetchCurrencies();
    fetchAllWallets();
  }, []);

  return (
    <>
      {/* Wallets Container */}
      <div className="flex gap-6 w-full overflow-x-auto pb-4">
        {walletsCardsData?.wallets?.map((wallet, i) => {
          const style = cardStyles[i % cardStyles.length];

          return (
            <div
              key={wallet.id}
              className={`bg-gradient-to-br ${style.gradient} 
                rounded-3xl min-w-[300px] p-6 text-white shadow-xl relative overflow-hidden
                backdrop-blur-xl`}
            >
              {/* Background Pattern */}
              <div className="absolute inset-0">
                <div className="absolute top-0 right-0 w-40 h-40 bg-white/5 rounded-full translate-x-16 -translate-y-16"></div>
                <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/5 rounded-full -translate-x-10 translate-y-10"></div>
              </div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <div
                      className={`w-12 h-12 ${style.iconBg} rounded-2xl flex items-center justify-center backdrop-blur-sm shadow-lg`}
                    >
                      {wallet.icon && wallet.icon !== "null" ? (
                        <img
                          src={wallet.icon}
                          alt={wallet.name}
                          className="w-7 h-7"
                        />
                      ) : (
                        <span className="font-bold text-lg">
                          {wallet.symbol}
                        </span>
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg leading-tight">
                        {wallet.name}
                      </h3>
                      <p className="text-white/70 text-sm font-medium">
                        {wallet.code}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Balance Section */}
                <div className="mb-6">
                  <p className="text-white/70 text-sm font-medium mb-2">
                    Current Balance
                  </p>
                  <h2 className="text-2xl font-bold leading-tight mb-1">
                    {wallet.formatted_balance}
                  </h2>
                  <p className="text-white/80 text-lg font-medium">
                    {wallet.code}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <button
                    className={`${style.accent} px-0 py-2 rounded-lg text-sm font-semibold 
                    transition-all duration-200 flex-1 backdrop-blur-sm border border-white/10
                    hover:scale-105 active:scale-95 shadow-lg`}
                  >
                    <span className="flex items-center justify-center gap-2">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 4v16m8-8H4"
                        />
                      </svg>
                      Top Up
                    </span>
                  </button>
                  <button
                    className={`${style.accent} px-3 py-2 rounded-lg text-sm font-semibold 
                    transition-all duration-200 flex-1 backdrop-blur-sm border border-white/10
                    hover:scale-105 active:scale-95 shadow-lg`}
                  >
                    <span className="flex items-center justify-center gap-2">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M20 12H4"
                        />
                      </svg>
                      Withdraw
                    </span>
                  </button>
                </div>
              </div>
            </div>
          );
        })}

        {/* Add Wallet Card */}
        <div className="border-2 border-dashed border-gray-300 rounded-3xl bg-gray-50/80 backdrop-blur-sm p-4 flex items-center justify-center min-w-[280px] hover:border-indigo-400 hover:bg-indigo-50/50 transition-all duration-300 group cursor-pointer">
          <button
            onClick={openModal}
            className="flex flex-col items-center justify-center text-gray-500 group-hover:text-indigo-600 transition-colors duration-300 w-full h-full"
          >
            <div className="w-14 h-14 border-2 border-dashed border-current rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 group-hover:border-solid group-hover:bg-indigo-100/50">
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
            </div>
            <p className="font-semibold text-lg mb-1">Add New Wallet</p>
            <p className="text-sm opacity-70 text-center px-4">
              Create a wallet for a new currency
            </p>
          </button>
        </div>
      </div>

      {/* Create Wallet Modal */}
      {showModal && (
        <div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm flex justify-center items-center z-50 px-4"
          onClick={(e) => e.target === e.currentTarget && closeModal()}
        >
          <div className="bg-white rounded-3xl w-full max-w-md shadow-2xl animate-[slideUp_0.3s_ease-out]">
            <div className="p-8">
              {/* Modal Header */}
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Create New Wallet
                </h2>
                <p className="text-gray-600">
                  Choose a currency for your new wallet
                </p>
              </div>

              {/* Currency Select */}
              <div className="mb-8">
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Select Currency
                </label>
                <div className="relative">
                  <select
                    className="w-full py-2 px-2 border-2 border-gray-200 rounded-2xl bg-white text-gray-900 font-medium focus:border-indigo-500 focus:outline-none transition-colors duration-200 appearance-none cursor-pointer hover:border-gray-300"
                    value={selectedCurrency}
                    onChange={(e) => setSelectedCurrency(e.target.value)}
                    disabled={loading}
                  >
                    <option value="" disabled>
                      Choose a currency...
                    </option>
                    {getAvailableCurrencies().map((c) => (
                      <option key={c.id} value={c.id}>
                        {c.name} ({c.code})
                      </option>
                    ))}
                  </select>
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <svg
                      className="w-5 h-5 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4">
                <button
                  onClick={closeModal}
                  disabled={loading}
                  className="flex-1 h-[50px] px-6 border-2 border-gray-200 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 hover:border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={createWallet}
                  disabled={loading || !selectedCurrency}
                  className="flex-1 h-[50px] px-6 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-lg hover:from-indigo-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
                >
                  {loading ? (
                    <span className="flex items-center justify-center gap-2">
                      <svg
                        className="animate-spin w-5 h-5"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Creating...
                    </span>
                  ) : (
                    <span className="flex items-center justify-center gap-2">
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                        />
                      </svg>
                      Create Wallet
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default WalletsCards;
