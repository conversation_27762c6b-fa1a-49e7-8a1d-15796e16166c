import React from "react";

const Badge = ({
  variant = "light",
  color = "primary",
  size = "md",
  startIcon,
  endIcon,
  children,
}) => {
  const baseStyles =
    "inline-flex items-center px-2.5 py-0.5 justify-center gap-1 rounded-full font-medium";

  // Define size styles
  const sizeStyles = {
    sm: "text-theme-xs",
    md: "text-sm",
  };

  // Define color styles for variants
  const variants = {
    light: {
      primary: "bg-primary/10 text-primary",
      success: "bg-success-50 text-success",
      error: "bg-error-50 text-error",
      warning: "bg-warning-50 text-warning",
      info: "bg-info-50 text-info",
      light: "bg-gray-100 text-gray-700",
      dark: "bg-gray-500 text-white",
    },
    solid: {
      primary: "bg-primary text-white",
      success: "bg-success text-white",
      error: "bg-error text-white",
      warning: "bg-warning text-white",
      info: "bg-info text-white",
      light: "bg-gray-400 text-white",
      dark: "bg-gray-700 text-white",
    },
  };

  // Get styles based on size and color variant
  const sizeClass = sizeStyles[size];
  const colorStyles = variants[variant][color];

  return (
    <span className={`${baseStyles} ${sizeClass} ${colorStyles}`}>
      {startIcon && <span className="mr-1">{startIcon}</span>}
      {children}
      {endIcon && <span className="ml-1">{endIcon}</span>}
    </span>
  );
};

export default Badge;
