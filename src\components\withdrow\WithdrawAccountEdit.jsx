"use client";

import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";

function WithdrawAccountEdit({ accountId }) {
  const networkService = new NetworkService();
  const router = useRouter();

  // State for the specific account being edited
  const [accountData, setAccountData] = useState(null);
  const [loading, setLoading] = useState(true);

  console.log("accountData", accountData);

  // Fetch specific account data
  const fetchAccountData = async () => {
    setLoading(true);

    try {
      const res = await networkService.get(
        `${ApiPath.withdrawAccounts}/${accountId}`
      );
      if (res.status === "completed") {
        setAccountData(res.data.data);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateAccount = async (e) => {
    e.preventDefault();

    const formData = new FormData();
    formData.append("method_name", e.target.method_name.value);
    formData.append("_method", "PUT");

    accountData.method.fields.forEach((field) => {
      const input = e.target[field.name];

      if (field.type === "file") {
        if (input.files && input.files[0]) {
          formData.append(`credentials[${field.name}][value]`, input.files[0]);
        }
      } else {
        formData.append(`credentials[${field.name}][value]`, input.value);
      }
      formData.append(`credentials[${field.name}][type]`, field.type);
      formData.append(
        `credentials[${field.name}][validation]`,
        field.validation
      );
    });

    try {
      const res = await networkService.postFormData(
        `${ApiPath.updateWithdrawAccount}${accountId}`,
        formData
      );

      if (res.status === "completed") {
        toast.success("Account updated successfully!");
        router.push("/withdraw/withdraw-account");
      } else {
        toast.error("Failed to update account.");
      }
    } catch (err) {
      console.error(err);
      toast.error("An error occurred while updating the account.");
    }
  };

  useEffect(() => {
    fetchAccountData();
  }, [accountId]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="mt-4 text-gray-600 dark:text-gray-400">
              Loading account data...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Main Form */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <Link
                href="/withdraw/withdraw-account"
                className="inline-flex items-center p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Edit Withdrawal Account
              </h2>
            </div>
          </div>
          <div className="grid grid-cols-12">
            <div className="col-span-6">
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                    Edit Withdrawal Account
                  </h2>
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Edit your withdrawal account details.
                  </p>
                </div>
                <div className="p-6">
                  <form
                    onSubmit={handleUpdateAccount}
                    className="space-y-4 mt-4"
                  >
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Method Name
                      </label>
                      <input
                        type="text"
                        name="method_name"
                        defaultValue={accountData?.method_name} // <-- DB value set here
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    {accountData?.method?.fields?.map((field, idx) => (
                      <div key={idx}>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          {field.name}
                        </label>

                        {field.type === "text" && (
                          <input
                            type="text"
                            name={field.name}
                            defaultValue={field.value} // <-- shows DB value
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          />
                        )}

                        {field.type === "textarea" && (
                          <textarea
                            name={field.name}
                            defaultValue={field.value} // <-- shows DB value
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          />
                        )}

                        {field.type === "file" && (
                          <input
                            type="file"
                            name={field.name}
                            className="mt-1 block w-full text-gray-700 border p-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          />
                        )}
                      </div>
                    ))}

                    <button
                      type="submit"
                      className="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Update Account
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default WithdrawAccountEdit;
