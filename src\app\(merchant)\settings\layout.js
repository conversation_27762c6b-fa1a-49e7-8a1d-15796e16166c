"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  UserIcon,
  ShieldCheckIcon,
  BellIcon,
  Cog6ToothIcon
} from "@heroicons/react/24/outline";

const AccountLayout = ({ children }) => {
  const pathname = usePathname();

  const isActive = (path) => {
    return pathname === path;
  };

  const tabs = [
    {
      name: "Profile",
      href: "/settings",
      icon: <UserIcon className="w-5 h-5" />,
      current: isActive("/settings")
    },
    {
      name: "Security",
      href: "/settings/security",
      icon: <ShieldCheckIcon className="w-5 h-5" />,
      current: isActive("/settings/security")
    },
    {
      name: "Notifications",
      href: "/settings/notifications",
      icon: <BellIcon className="w-5 h-5" />,
      current: isActive("/settings/notifications")
    },
    {
      name: "Preferences",
      href: "/settings/preferences",
      icon: <Cog6ToothIcon className="w-5 h-5" />,
      current: isActive("/settings/preferences")
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Account Settings
        </h1>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <Link
              key={tab.name}
              href={tab.href}
              className={`group inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${tab.current
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
            >
              <span className={`mr-2 ${tab.current ? "text-blue-500" : "text-gray-400 group-hover:text-gray-500"}`}>
                {tab.icon}
              </span>
              {tab.name}
            </Link>
          ))}
        </nav>
      </div>

      {/* Page Content */}
      <div>{children}</div>
    </div>
  );
};

export default AccountLayout;
