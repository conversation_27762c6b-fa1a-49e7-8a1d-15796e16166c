"use client";

import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import {
  ArrowDownCircleIcon,
  ArrowUpCircleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";
import React, { useEffect, useState } from "react";

function AllNotification() {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);

  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10); // default value set
  const [lastPage, setLastPage] = useState(1);

  const networkService = new NetworkService();

  // fetch all notifications
  const fetchAllNotifications = async () => {
    try {
      setLoading(true);
      const res = await networkService.get(ApiPath.getNotifications, {
        for: "merchant",
        page: currentPage,
        per_page: perPage,
      });
      if (res.status === "completed") {
        const meta = res.data.data.meta;
        setPerPage(meta.per_page);
        setLastPage(meta.last_page);
        setCurrentPage(meta.current_page);
        setNotifications(res.data.data.notifications);
      }
    } finally {
      setLoading(false);
    }
  };

  // if currentPage change then fetch data
  useEffect(() => {
    fetchAllNotifications();
  }, [currentPage]);

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < lastPage) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const handlePageClick = (pageNum) => {
    setCurrentPage(pageNum);
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case "merchant_withdraw_approved":
        return (
          <CheckCircleIcon className="w-10 h-10 text-green-500 bg-green-100 rounded-full p-2" />
        );
      case "merchant_payment":
        return (
          <ArrowDownCircleIcon className="w-10 h-10 text-blue-500 bg-blue-100 rounded-full p-2" />
        );
      case "merchant_withdraw_request":
        return (
          <ArrowUpCircleIcon className="w-10 h-10 text-yellow-500 bg-yellow-100 rounded-full p-2" />
        );
      default:
        return (
          <ExclamationTriangleIcon className="w-10 h-10 text-gray-500 bg-gray-100 rounded-full p-2" />
        );
    }
  };

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
          <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            Mark All as Read
          </button>
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              Recent Notifications
            </h2>
            <p className="mt-1 text-sm text-gray-600">
              Stay updated with your account activities and system updates.
            </p>
          </div>
          <div className="p-6">
            {loading ? (
              <div className="text-center text-gray-500">
                Loading notifications...
              </div>
            ) : (
              <div className="space-y-4">
                <ul className="flex flex-col gap-2 h-auto overflow-y-auto custom-scrollbar">
                  {notifications.length > 0 ? (
                    notifications.map((notification, index) => (
                      <li key={notification.id || index}>
                        <div
                          className={`flex gap-3 rounded-lg flex-wrap items-center justify-between border p-3 cursor-pointer ${
                            notification.is_read
                              ? "bg-gray-50 border-gray-200"
                              : " bg-blue-50 border border-blue-200 hover:bg-gray-100"
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            <span className="shrink-0">
                              {getNotificationIcon(notification.type)}
                            </span>
                            <span className="block">
                              <span className="mb-1.5 block text-theme-sm text-gray-500">
                                <span className="font-medium text-gray-800/90">
                                  {notification.title}
                                </span>
                              </span>
                              <span className="text-gray-600 text-sm">
                                {notification.message}
                              </span>
                              <span className="flex items-center gap-2 mt-1 text-gray-500 text-theme-xs">
                                <span>
                                  {notification.type.replace(/_/g, " ")}
                                </span>
                                <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                                <span>{notification.created_at}</span>
                              </span>
                            </span>
                          </div>
                          <div>
                            <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                              Explore
                            </button>
                          </div>
                        </div>
                      </li>
                    ))
                  ) : (
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <p className="text-gray-500">
                        No more notifications to show.
                      </p>
                    </div>
                  )}
                </ul>

                {/* Pagination */}
                {notifications.length > 0 && lastPage > 1 && (
                  <div className="mt-6 flex items-center justify-between border-t border-gray-200 pt-4">
                    <div className="flex items-center space-x-4">
                      {/* Previous Button */}
                      <button
                        onClick={handlePrevPage}
                        className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={currentPage === 1}
                      >
                        <ChevronLeftIcon className="w-3 h-3" />
                        Previous
                      </button>

                      {/* Page Numbers */}
                      <div className="flex items-center space-x-2">
                        {[...Array(lastPage)].map((_, i) => {
                          const pageNum = i + 1;
                          return (
                            <button
                              key={pageNum}
                              onClick={() => handlePageClick(pageNum)}
                              className={`inline-flex items-center justify-center w-8 h-8 text-sm font-medium rounded-lg hover:bg-gray-50 hover:text-gray-900 ${
                                currentPage === pageNum
                                  ? "bg-blue-600 text-white hover:"
                                  : "text-gray-700 bg-white border border-gray-300"
                              }`}
                            >
                              {pageNum}
                            </button>
                          );
                        })}
                      </div>

                      {/* Next Button */}
                      <button
                        onClick={handleNextPage}
                        disabled={currentPage === lastPage}
                        className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 focus:outline-none"
                      >
                        Next
                        <ChevronRightIcon className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default AllNotification;
