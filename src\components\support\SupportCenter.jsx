"use client";

import React from "react";
import Link from "next/link";
import {
  TicketIcon,
  ChatBubbleLeftRightIcon,
  ListBulletIcon,
  QuestionMarkCircleIcon,
  DocumentTextIcon,
  VideoCameraIcon,
} from "@heroicons/react/24/outline";

function SupportCenter() {
  const supportOptions = [
    {
      title: "Create Support Ticket",
      description:
        "Submit a new support request for technical issues or account help",
      icon: TicketIcon,
      href: "/support/create",
      color: "bg-blue-500",
      hoverColor: "hover:bg-blue-600",
    },
    {
      title: "My Support Tickets",
      description: "View and manage your existing support tickets",
      icon: ListBulletIcon,
      href: "/support/tickets",
      color: "bg-green-500",
      hoverColor: "hover:bg-green-600",
    },
    {
      title: "Live Chat Support",
      description: "Chat with our support team in real-time",
      icon: ChatBubbleLeftRightIcon,
      href: "/support/chat",
      color: "bg-purple-500",
      hoverColor: "hover:bg-purple-600",
    },
  ];

  const quickHelp = [
    {
      title: "Frequently Asked Questions",
      description: "Find answers to common questions about Money Chain",
      icon: QuestionMarkCircleIcon,
      color: "text-blue-600",
    },
    {
      title: "Documentation",
      description: "Comprehensive guides and API documentation",
      icon: DocumentTextIcon,
      color: "text-green-600",
    },
    {
      title: "Video Tutorials",
      description: "Step-by-step video guides to help you get started",
      icon: VideoCameraIcon,
      color: "text-purple-600",
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Support Center
        </h1>
        <p className="mt-2 text-lg text-gray-600 dark:text-gray-400">
          We're here to help you with any questions or issues you may have
        </p>
      </div>

      {/* Support Options */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {supportOptions.map((option, index) => (
          <Link
            key={index}
            href={option.href}
            className="group bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 border border-gray-200 dark:border-gray-700"
          >
            <div className="p-6">
              <div className="flex items-center justify-center w-12 h-12 rounded-lg mb-4 mx-auto">
                <div
                  className={`${option.color} ${option.hoverColor} p-3 rounded-lg group-hover:scale-110 transition-transform duration-200`}
                >
                  <option.icon className="w-6 h-6 text-white" />
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white text-center mb-2">
                {option.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                {option.description}
              </p>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}

export default SupportCenter;
