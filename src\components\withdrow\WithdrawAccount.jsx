"use client";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import React, { useEffect, useState, useMemo, useCallback } from "react";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { toast } from "react-toastify";
import { useModal } from "@/hooks/useModal";
import { Modal } from "../ui/modal";

function WithdrawAccount() {
  const networkService = new NetworkService();

  const [withdrawAccounts, setWithdrawAccounts] = useState([]);
  const [loading, setLoading] = useState(true);

  // Search & pagination
  const [searchTerm, setSearchTerm] = useState("");
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Modal state
  const { isOpen, openModal, closeModal } = useModal();
  const [accountToDelete, setAccountToDelete] = useState(null);

  // Fetch accounts
  const fetchWithdrawAccounts = async () => {
    try {
      const res = await networkService.get(ApiPath.withdrawAccounts);
      if (res.status === "completed") {
        setWithdrawAccounts(res.data.data.accounts);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWithdrawAccounts();
  }, []);

  // Filtered accounts
  const filteredAccounts = useMemo(() => {
    if (!searchTerm.trim()) return withdrawAccounts;

    return withdrawAccounts.filter(
      (account) =>
        account.method.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        account.currency.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [withdrawAccounts, searchTerm]);

  // Paginated accounts
  const paginatedAccounts = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredAccounts.slice(startIndex, endIndex);
  }, [filteredAccounts, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredAccounts.length / itemsPerPage);

  // Search handler
  const handleSearch = () => {
    setCurrentPage(1);
  };

  // Per-page change
  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Delete account
  const handleDelete = useCallback(
    async (id) => {
      try {
        await networkService.delete(`${ApiPath.deleteWithdrawAccount}${id}`);
        toast.success("Account deleted successfully!");
        closeModal();
        fetchWithdrawAccounts();
      } finally {
        setAccountToDelete(null);
      }
    },
    [closeModal, networkService]
  );

  // Open delete modal
  const handleOpenModal = useCallback(
    (account) => {
      setAccountToDelete(account);
      openModal();
    },
    [openModal]
  );

  return (
    <div className="space-y-6">
      {/* Withdrawal Accounts */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Withdrawal All Accounts
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            View and manage your withdrawal accounts.
          </p>
        </div>

        {/* Search & Filters */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            {/* Search */}
            <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search by account name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
              <button
                onClick={handleSearch}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Search
              </button>
            </div>

            {/* Per-page selector */}
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Show:
              </label>
              <select
                value={itemsPerPage}
                onChange={(e) =>
                  handleItemsPerPageChange(Number(e.target.value))
                }
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
              <span className="text-sm text-gray-700 dark:text-gray-300">
                per page
              </span>
            </div>

            <Link
              href="/withdraw/withdraw-account/create"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Add New Account
            </Link>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          {loading ? (
            <div className="p-6 text-center text-gray-500">Loading...</div>
          ) : filteredAccounts.length > 0 ? (
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    SL NO
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Currency
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedAccounts.map((account, index) => (
                  <tr
                    key={account.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                      {(currentPage - 1) * itemsPerPage + index + 1}
                    </td>
                    <td className="px-6 py-4 flex items-center gap-2">
                      <img
                        src={account.method.icon}
                        alt={account.method.name}
                        className="w-6 h-6 rounded"
                      />
                      <span className="text-sm text-gray-900 dark:text-white">
                        {account.method.name}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                      {account.currency}
                    </td>
                    <td className="px-6 py-4 text-sm font-medium">
                      <Link
                        onClick={() => console.log(account.id)}
                        href={`/withdraw/withdraw-account/edit/${account.id}`}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => {
                          handleOpenModal(account);
                        }}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="p-6">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  {searchTerm
                    ? "No accounts match your search."
                    : "No withdrawal accounts found."}
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                  {searchTerm
                    ? "Try adjusting your search terms."
                    : "Your withdrawal accounts will appear here."}
                </p>
                {searchTerm && (
                  <button
                    onClick={() => {
                      setSearchTerm("");
                      setCurrentPage(1);
                    }}
                    className="mt-3 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
                  >
                    Clear search
                  </button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Pagination */}
        {!loading && filteredAccounts.length > 0 && totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
                >
                  Previous
                </button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${
                          currentPage === pageNum
                            ? "bg-blue-600 text-white"
                            : "text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() =>
                    setCurrentPage(Math.min(totalPages, currentPage + 1))
                  }
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Delete Modal */}
      {isOpen && accountToDelete && (
        <Modal
          showCloseButton={false}
          isOpen={isOpen}
          onClose={closeModal}
          className="max-w-lg m-4"
        >
          <div className="relative w-full rounded-3xl bg-white p-6 dark:bg-gray-900">
            <div className="text-center">
              {/* Warning Icon */}
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                <svg
                  className="h-6 w-6 text-red-600 dark:text-red-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
                  />
                </svg>
              </div>

              <h3 className="mt-4 text-lg font-semibold text-gray-900 dark:text-white">
                Delete Withdrawal Account
              </h3>

              <div className="mt-2">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Are you sure you want to delete this withdrawal account?
                </p>
                {accountToDelete && (
                  <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-center gap-2">
                      <img
                        src={accountToDelete.method.icon}
                        alt={accountToDelete.method.name}
                        className="w-5 h-5 rounded"
                      />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {accountToDelete.method.name}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        ({accountToDelete.currency})
                      </span>
                    </div>
                  </div>
                )}
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  This action cannot be undone.
                </p>
              </div>

              <div className="mt-6 flex flex-col-reverse gap-3 sm:flex-row sm:justify-center">
                <button
                  type="button"
                  onClick={closeModal}
                  className="inline-flex justify-center items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={() => handleDelete(accountToDelete.id)}
                  className="inline-flex justify-center items-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 dark:focus:ring-red-400"
                >
                  Yes, Delete Account
                </button>
              </div>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}

export default WithdrawAccount;
