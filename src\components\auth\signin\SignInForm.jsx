"use client";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/16/solid";
import Image from "next/image";
import React, { useState } from "react";
import SiteLogo from "../../../../public/assets/logo/logo.svg";
import Link from "next/link";
import NetworkService from "@/network/service/network_service";
import { toast } from "react-toastify";
import ApiPath from "@/network/api/api_path";
import { useRouter } from "next/navigation";
import { useSettings } from "@/context/settingsContext";
import { getSettingValue } from "@/utils/utils";

function SigninForm() {
  // use network
  const network = new NetworkService();
  const router = useRouter();

  // state
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [keepLoggedIn, setKeepLoggedIn] = useState(false);

  const { settings } = useSettings();
  const siteTwoFa = getSettingValue(settings, "fa_verification");

  // password toggle
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSignIn = async (e) => {
    e.preventDefault();
    // Validation
    if (!email.trim()) {
      toast.error("Email is required!");
      return;
    }
    if (!password.trim()) {
      toast.error("Password is required!");
      return;
    }

    // body request
    try {
      const requestBody = {
        email,
        password,
      };
      const res = await network.globalPost(ApiPath.signin, requestBody);
      if (res.status === "completed") {
        const token = res.data.data.token;
        network.tokenService.saveAccessToken(token);
        // check 2FA condition
        if (siteTwoFa === "1") {
          router.push("/auth/two-fa-verify");
        } else {
          router.push("/dashboard");
          toast.success(res.data.message);
        }
      }
    } finally {
    }
  };

  return (
    <>
      <div className="min-h-screen flex">
        {/* Left Side */}
        <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-white p-5">
          <div className="max-w-md w-full space-y-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 text-sm mb-8 hover:text-primary"
            >
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              <span>Back to dashboard</span>
            </Link>

            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">Sign In</h2>
              <p className="text-gray-600">
                Enter your email and password to sign in!
              </p>
            </div>

            <div className="space-y-6">
              <form onSubmit={handleSignIn} className="space-y-4">
                {/* Email Field */}
                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                  />
                </div>

                {/* Password Field */}
                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Password <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center z-10"
                      onClick={togglePasswordVisibility}
                    >
                      {showPassword ? (
                        <EyeIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                      ) : (
                        <EyeSlashIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Keep me logged in & Forgot password */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      id="keep-logged-in"
                      name="keep-logged-in"
                      type="checkbox"
                      checked={keepLoggedIn}
                      onChange={(e) => setKeepLoggedIn(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label
                      htmlFor="keep-logged-in"
                      className="ml-2 block text-sm text-gray-700"
                    >
                      Keep me logged in
                    </label>
                  </div>
                  <div className="text-sm">
                    <a
                      href="#"
                      className="font-medium text-blue-600 hover:primary"
                    >
                      Forgot password?
                    </a>
                  </div>
                </div>

                {/* Sign In Button */}
                <div>
                  <button
                    type="submit"
                    className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                  >
                    Sign In
                  </button>
                </div>

                {/* Sign Up Link */}
                <div className="text-center text-sm text-gray-600">
                  Don't have an account?{" "}
                  <a
                    href="#"
                    className="font-medium text-blue-600 hover:primary"
                  >
                    Sign Up
                  </a>
                </div>
              </form>
            </div>
          </div>
        </div>
        {/* Right Side */}
        <div className="hidden lg:block flex-1 bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 relative">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white space-y-4">
              {/* Logo/Icon */}
              <div className="flex justify-center">
                <Image
                  src={SiteLogo}
                  alt="Picture of the author"
                  width={"auto"}
                  height={50}
                />
              </div>
              <p className="text-blue-200 text-lg px-8">
                Free and Open-Source Tailwind CSS Admin Dashboard Template
              </p>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-20 left-20 w-32 h-32 bg-blue-400 rounded-full opacity-10"></div>
          <div className="absolute bottom-20 right-20 w-24 h-24 bg-indigo-400 rounded-full opacity-10"></div>
          <div className="absolute top-1/2 left-10 w-16 h-16 bg-blue-300 rounded-full opacity-5"></div>
        </div>
      </div>
    </>
  );
}

export default SigninForm;
