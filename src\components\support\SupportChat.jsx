"use client";

import React, { useState, useRef, useEffect } from "react";
import Link from "next/link";
import {
  ArrowLeftIcon,
  PaperAirplaneIcon,
  PaperClipIcon,
  FaceSmileIcon,
  PhoneIcon,
  VideoCameraIcon,
  InformationCircleIcon,
  UserCircleIcon,
  CheckIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";

function SupportChat() {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState([
    {
      id: 1,
      sender: "support",
      name: "<PERSON>",
      avatar: "/avatars/support-1.jpg",
      message:
        "Hello! I'm <PERSON> from Money Chain support. I see you're having issues with payment processing. How can I help you today?",
      timestamp: "2024-01-16 10:30 AM",
      status: "delivered",
    },
    {
      id: 2,
      sender: "user",
      name: "<PERSON>",
      message:
        "Hi <PERSON>! Yes, I'm having trouble with credit card payments. They keep getting declined even though the cards are valid.",
      timestamp: "2024-01-16 10:32 AM",
      status: "delivered",
    },
    {
      id: 3,
      sender: "support",
      name: "<PERSON>",
      avatar: "/avatars/support-1.jpg",
      message:
        "I understand how frustrating that must be. Let me check your account settings and recent transaction logs. Can you tell me approximately when this issue started?",
      timestamp: "2024-01-16 10:33 AM",
      status: "delivered",
    },
    {
      id: 4,
      sender: "user",
      name: "You",
      message:
        "It started yesterday around 2 PM. Before that, everything was working fine.",
      timestamp: "2024-01-16 10:35 AM",
      status: "delivered",
    },
    {
      id: 5,
      sender: "support",
      name: "Sarah Johnson",
      avatar: "/avatars/support-1.jpg",
      message:
        "Thank you for that information. I can see there was a configuration change in your payment gateway settings yesterday. Let me fix that for you right now.",
      timestamp: "2024-01-16 10:37 AM",
      status: "delivered",
    },
    {
      id: 6,
      sender: "support",
      name: "Sarah Johnson",
      avatar: "/avatars/support-1.jpg",
      message:
        "I've updated your payment gateway configuration. Can you please try processing a test transaction now?",
      timestamp: "2024-01-16 10:40 AM",
      status: "delivered",
    },
  ]);

  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (message.trim()) {
      const newMessage = {
        id: messages.length + 1,
        sender: "user",
        name: "You",
        message: message.trim(),
        timestamp: new Date().toLocaleString(),
        status: "sending",
      };

      setMessages([...messages, newMessage]);
      setMessage("");

      // Simulate message delivery
      setTimeout(() => {
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === newMessage.id ? { ...msg, status: "delivered" } : msg
          )
        );
      }, 1000);

      // Simulate support typing
      setTimeout(() => {
        setIsTyping(true);
        setTimeout(() => {
          setIsTyping(false);
          const supportResponse = {
            id: messages.length + 2,
            sender: "support",
            name: "Sarah Johnson",
            avatar: "/avatars/support-1.jpg",
            message: "Thanks for the update! I'll look into that right away.",
            timestamp: new Date().toLocaleString(),
            status: "delivered",
          };
          setMessages((prev) => [...prev, supportResponse]);
        }, 2000);
      }, 1500);
    }
  };

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    // Handle file upload logic here
    console.log("Files selected:", files);
  };

  const getMessageStatus = (status) => {
    switch (status) {
      case "sending":
        return <ClockIcon className="w-3 h-3 text-gray-400" />;
      case "delivered":
        return <CheckIcon className="w-3 h-3 text-blue-500" />;
      case "read":
        return <CheckIcon className="w-3 h-3 text-green-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              href="/support/tickets"
              className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <ArrowLeftIcon className="w-4 h-4 mr-1" />
              Back to Tickets
            </Link>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <UserCircleIcon className="w-6 h-6 text-white" />
              </div>
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-400 border-2 border-white dark:border-gray-800 rounded-full"></div>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                Sarah Johnson
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Support Agent • Online
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
              <PhoneIcon className="w-5 h-5" />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
              <VideoCameraIcon className="w-5 h-5" />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
              <InformationCircleIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Ticket Info Banner */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <InformationCircleIcon className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-blue-800 dark:text-blue-200">
              Ticket #TKT-001: Payment processing issue with credit cards
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
              In Progress
            </span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
              High Priority
            </span>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto space-y-4">
          {messages.map((msg) => (
            <div
              key={msg.id}
              className={`flex ${
                msg.sender === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`flex max-w-xs lg:max-w-md ${
                  msg.sender === "user" ? "flex-row-reverse" : "flex-row"
                } space-x-2`}
              >
                {msg.sender === "support" && (
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <UserCircleIcon className="w-5 h-5 text-white" />
                  </div>
                )}

                <div className={`${msg.sender === "user" ? "mr-2" : "ml-2"}`}>
                  <div
                    className={`px-4 py-2 rounded-lg ${
                      msg.sender === "user"
                        ? "bg-blue-600 text-white"
                        : "bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700"
                    }`}
                  >
                    <p className="text-sm">{msg.message}</p>
                  </div>

                  <div
                    className={`flex items-center mt-1 space-x-1 ${
                      msg.sender === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {msg.timestamp}
                    </span>
                    {msg.sender === "user" && getMessageStatus(msg.status)}
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* Typing Indicator */}
          {isTyping && (
            <div className="flex justify-start">
              <div className="flex space-x-2 ml-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <UserCircleIcon className="w-5 h-5 text-white" />
                </div>
                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-4 py-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Message Input */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
        <form onSubmit={handleSendMessage} className="max-w-4xl mx-auto">
          <div className="flex items-end space-x-3">
            <div className="flex-1">
              <div className="relative">
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your message..."
                  rows="1"
                  className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none"
                  onKeyPress={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage(e);
                    }
                  }}
                />
                <div className="absolute right-2 bottom-2 flex items-center space-x-1">
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
                  >
                    <PaperClipIcon className="w-4 h-4" />
                  </button>
                  <button
                    type="button"
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
                  >
                    <FaceSmileIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={!message.trim()}
              className="p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <PaperAirplaneIcon className="w-5 h-5" />
            </button>
          </div>
        </form>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,.pdf,.doc,.docx"
          onChange={handleFileUpload}
          className="hidden"
        />
      </div>
    </div>
  );
}

export default SupportChat;
