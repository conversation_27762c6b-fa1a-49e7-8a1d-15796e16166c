// calculate min and max amount helper function
export const dynamicDecimals = ({
  currencyCode,
  siteCurrencyCode,
  siteCurrencyDecimals,
  isCrypto,
}) => {
  const dynamicCurrency = currencyCode === siteCurrencyCode;
  // console.log("dynamicCurrency", dynamicCurrency);

  if (dynamicCurrency) {
    // console.log("siteCurrencyDecimals", siteCurrencyDecimals);
    return siteCurrencyDecimals;
  } else {
    // console.log("isCrypto", isCrypto);
    return isCrypto ? 8 : 2;
  }
};
