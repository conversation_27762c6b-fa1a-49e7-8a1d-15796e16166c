import React from "react";

const DonutChart = () => {
  // Data for the chart
  const data = [
    { label: "Success", value: 45, color: "#4F46E5" },
    { label: "Pending", value: 25, color: "#8B5CF6" },
    { label: "Waiting", value: 30, color: "#E5E7EB" },
  ];

  const total = data.reduce((sum, item) => sum + item.value, 0);
  const radius = 80;
  const innerRadius = 50;
  const centerX = 120;
  const centerY = 120;

  // Calculate paths for each segment
  const createArcPath = (startAngle, endAngle, outerRadius, innerRadius) => {
    const startAngleRad = (startAngle - 90) * (Math.PI / 180);
    const endAngleRad = (endAngle - 90) * (Math.PI / 180);

    const x1 = centerX + outerRadius * Math.cos(startAngleRad);
    const y1 = centerY + outerRadius * Math.sin(startAngleRad);
    const x2 = centerX + outerRadius * Math.cos(endAngleRad);
    const y2 = centerY + outerRadius * Math.sin(endAngleRad);

    const x3 = centerX + innerRadius * Math.cos(endAngleRad);
    const y3 = centerY + innerRadius * Math.sin(endAngleRad);
    const x4 = centerX + innerRadius * Math.cos(startAngleRad);
    const y4 = centerY + innerRadius * Math.sin(startAngleRad);

    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

    return [
      "M",
      x1,
      y1,
      "A",
      outerRadius,
      outerRadius,
      0,
      largeArcFlag,
      1,
      x2,
      y2,
      "L",
      x3,
      y3,
      "A",
      innerRadius,
      innerRadius,
      0,
      largeArcFlag,
      0,
      x4,
      y4,
      "Z",
    ].join(" ");
  };

  let currentAngle = 0;
  const segments = data.map((item) => {
    const angle = (item.value / total) * 360;
    const path = createArcPath(
      currentAngle,
      currentAngle + angle,
      radius,
      innerRadius
    );
    currentAngle += angle;
    return { ...item, path };
  });

  return (
    <div className="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]">
      <div className="flex flex-col items-center">
        {/* SVG Chart */}
        <div className="relative">
          <svg width="240" height="240" className="transform">
            {segments.map((segment, index) => (
              <path
                key={index}
                d={segment.path}
                fill={segment.color}
                stroke="white"
                strokeWidth="2"
                className="transition-all duration-300 hover:opacity-80"
              />
            ))}
          </svg>

          {/* Center Label */}
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <span className="text-gray-600 text-sm font-medium">Payment </span>
            <span className="text-gray-900 text-3xl font-semibold">45%</span>
          </div>
        </div>

        {/* Legend */}
        <div className="flex items-center justify-center space-x-4 pb-6">
          {data.map((item, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: item.color }}
              ></div>
              <span className="text-gray-600 text-sm">{item.label}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DonutChart;
