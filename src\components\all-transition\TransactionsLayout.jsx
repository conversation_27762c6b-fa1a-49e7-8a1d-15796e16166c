"use client";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import React, { useEffect, useState } from "react";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "../ui";
import Badge from "../ui/badge/Badge";
import tableDesImg from "../../../public/assets/user/product-01.webp";
import Image from "next/image";

function TransactionsLayout() {
  // use network
  const networkService = new NetworkService();
  const [transitionData, setTransitionData] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch transition data
  const fetchTransitionData = async () => {
    setLoading(true);
    try {
      const res = await networkService.get(ApiPath.recentTransition);
      if (res.status === "completed") {
        setTransitionData(res.data.data.transactions);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransitionData();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Transactions
        </h1>
        <div className="flex space-x-3">
          <button className="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            Export
          </button>
          <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            Filter
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Transaction History
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            View all your transaction history and details.
          </p>
        </div>
        <div className="p-0">
          <div className="overflow-x-auto">
            <Table>
              {/* Table Header */}
              <TableHeader className="border-b bg-gray-50 border-gray-100 dark:border-white/[0.05]">
                <TableRow>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Description
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Transaction ID
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Type
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Amount
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Charge
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Status
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                  >
                    Method
                  </TableCell>
                </TableRow>
              </TableHeader>

              {/* Table Body */}
              <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                {loading ? (
                  <tr>
                    <td colSpan={7} className="text-center text-gray-400 py-6">
                      Loading...
                    </td>
                  </tr>
                ) : transitionData.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="text-center text-gray-400 py-6">
                      No transactions found.
                    </td>
                  </tr>
                ) : (
                  transitionData.map((item) => (
                    <TableRow key={item.tnx}>
                      <TableCell className="px-5 py-4 sm:px-6 text-start">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 overflow-hidden rounded-full">
                            <Image
                              width={40}
                              height={40}
                              src={tableDesImg}
                              alt="Transaction"
                            />
                          </div>
                          <div>
                            <span className="block font-medium text-gray-800 text-theme-sm dark:text-white/90">
                              {item?.description}
                            </span>
                            <span className="block text-gray-500 text-theme-xs dark:text-gray-400">
                              {item?.created_at}
                            </span>
                          </div>
                        </div>
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {item?.tnx}
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {item?.type}
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        <span
                          className={
                            item.is_plus ? "text-green-700" : "text-red-700"
                          }
                        >
                          {item.is_plus ? "+" : "-"}
                          {item.amount}
                          <span className="ml-1 text-xs">
                            {item.is_plus ? "↑" : "↓"}
                          </span>
                        </span>
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {item?.charge}
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        <Badge
                          size="sm"
                          variant="solid"
                          color={
                            item?.status === "Success"
                              ? "success"
                              : item?.status === "Pending"
                              ? "warning"
                              : "error"
                          }
                        >
                          {item?.status}
                        </Badge>
                      </TableCell>

                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {item?.method}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TransactionsLayout;
