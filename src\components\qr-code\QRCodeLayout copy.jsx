"use client";

import React, { useState } from "react";
import {
  QrCodeIcon,
  DocumentDuplicateIcon,
  ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";

function QRCodeLayout() {
  const [qrData, setQrData] = useState({
    amount: "",
    currency: "BTC",
    description: "",
    walletAddress: "",
  });
  const [generatedQR, setGeneratedQR] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setQrData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const generateQRCode = () => {
    // In a real implementation, you would generate an actual QR code here
    // For now, we'll simulate it
    const qrString = `moneychain:${qrData.walletAddress}?amount=${
      qrData.amount
    }&currency=${qrData.currency}&description=${encodeURIComponent(
      qrData.description
    )}`;
    setGeneratedQR({
      data: qrString,
      timestamp: new Date().toISOString(),
    });
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          QR Code Generator
        </h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* QR Code Generator Form */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Generate Payment QR Code
            </h2>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Create a QR code for receiving payments.
            </p>
          </div>
          <div className="p-6">
            <form
              className="space-y-4"
              onSubmit={(e) => {
                e.preventDefault();
                generateQRCode();
              }}
            >
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Wallet Address
                </label>
                <input
                  type="text"
                  name="walletAddress"
                  value={qrData.walletAddress}
                  onChange={handleInputChange}
                  placeholder="Enter your wallet address"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Amount
                  </label>
                  <input
                    type="number"
                    name="amount"
                    value={qrData.amount}
                    onChange={handleInputChange}
                    placeholder="0.00"
                    step="0.00000001"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Currency
                  </label>
                  <select
                    name="currency"
                    value={qrData.currency}
                    onChange={handleInputChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="BTC">Bitcoin (BTC)</option>
                    <option value="ETH">Ethereum (ETH)</option>
                    <option value="USDT">Tether (USDT)</option>
                    <option value="BNB">Binance Coin (BNB)</option>
                  </select>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Description (Optional)
                </label>
                <input
                  type="text"
                  name="description"
                  value={qrData.description}
                  onChange={handleInputChange}
                  placeholder="Payment description"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <button
                type="submit"
                className="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <QrCodeIcon className="w-5 h-5 mr-2" />
                Generate QR Code
              </button>
            </form>
          </div>
        </div>

        {/* Generated QR Code Display */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Generated QR Code
            </h2>
          </div>
          <div className="p-6">
            {generatedQR ? (
              <div className="space-y-4">
                {/* QR Code Placeholder - In real implementation, use a QR code library */}
                <div className="flex justify-center">
                  <div className="w-64 h-64 bg-gray-100 dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <QrCodeIcon className="w-16 h-16 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        QR Code Generated
                      </p>
                    </div>
                  </div>
                </div>

                {/* QR Code Data */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      QR Code Data:
                    </span>
                    <button
                      onClick={() => copyToClipboard(generatedQR.data)}
                      className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-800"
                    >
                      <DocumentDuplicateIcon className="w-3 h-3 mr-1" />
                      Copy
                    </button>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 break-all font-mono">
                    {generatedQR.data}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button className="flex-1 inline-flex justify-center items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                    <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                    Download
                  </button>
                  <button className="flex-1 inline-flex justify-center items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                    Share
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <QrCodeIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  Fill in the form and click "Generate QR Code" to create your
                  payment QR code.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Recent QR Codes */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Recent QR Codes
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Your recently generated QR codes.
          </p>
        </div>
        <div className="p-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <p className="text-gray-500 dark:text-gray-400">
              No recent QR codes found. Generate your first QR code above.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default QRCodeLayout;
