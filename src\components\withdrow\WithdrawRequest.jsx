"use client";
import ApiPath from "@/network/api/api_path";
import NetworkService from "@/network/service/network_service";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { dynamicDecimals } from "../../../utils.js";

function WithdrawRequest() {
  // use network
  const networkService = new NetworkService();

  // states
  const [walletData, setWalletData] = useState([]);
  const [selectedWallet, setSelectedWallet] = useState(null);
  const [withdrawAmount, setWithdrawAmount] = useState("");
  const [step, setStep] = useState(1);
  const [transferResponse, setTransferResponse] = useState(null);

  // Step Navigation
  const nextStep = () => setStep((s) => Math.min(s + 1, 3));
  const prevStep = () => setStep((s) => Math.max(s - 1, 1));
  const resetSteps = () => setStep(1);

  // fetch wallet data
  const fetchWithdrawData = async () => {
    try {
      const res = await networkService.get(ApiPath.withdrawAccounts);
      if (res.status === "completed") {
        setWalletData(res.data.data.accounts);
      }
    } catch (err) {
      toast.error("Failed to load wallet accounts");
    }
  };

  useEffect(() => {
    fetchWithdrawData();
  }, []);

  // wallet select
  const handleSelectWallet = (e) => {
    const wallet = walletData.find((w) => String(w.id) === e.target.value);
    setSelectedWallet(wallet || null);
  };

  // validation
  const handleValidated = () => {
    if (!selectedWallet) {
      toast.error("Please select a wallet");
      return false;
    }

    const amount = Number(withdrawAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return false;
    }

    const wallet = walletData.find((w) => w.id === selectedWallet.id);
    if (wallet) {
      const min = parseFloat(wallet?.method?.min_withdraw || "0");
      const max = parseFloat(wallet?.method?.max_withdraw || "0");
      const siteCurrencyCode = "USD"; // fallback
      const siteCurrencyDecimals = 2; // fallback

      const decimals = dynamicDecimals({
        currencyCode: wallet.currency,
        siteCurrencyCode,
        siteCurrencyDecimals,
        isCrypto: wallet.is_crypto,
      });

      if (amount < min || amount > max) {
        toast.error(
          `Amount must be between ${min.toFixed(decimals)} and ${max.toFixed(
            decimals
          )} ${wallet.currency}`
        );
        return false;
      }
    }
    return true;
  };

  // withdraw request
  const handleWithdrawRequest = async () => {
    try {
      const res = await networkService.post(ApiPath.withdrawRequest, {
        amount: withdrawAmount,
        withdraw_account_id: selectedWallet.id,
      });

      if (res.status === "completed") {
        toast.success(res.data.message);
        setTransferResponse(res.data.data.transaction);
        nextStep();
      } else {
        toast.error(res.data?.message || "Withdrawal failed");
      }
    } catch (err) {
      toast.error("Something went wrong!");
    }
  };

  // total amount calc
  const totalAmount = () => {
    if (!selectedWallet) return 0;
    const amount = Number(withdrawAmount);
    const charge = Number(selectedWallet?.method?.charge || 0);
    const chargeType = selectedWallet?.method?.charge_type || "fixed";
    return chargeType === "fixed"
      ? amount + charge
      : amount + (amount * charge) / 100;
  };

  return (
    <>
      <div className="flex items-center justify-between mb-5">
        <h2 className="text-xl font-semibold text-gray-900">Withdraw Funds</h2>
      </div>

      <div className="grid grid-cols-12">
        <div className="col-span-12 lg:col-span-5 lg:col-start-4">
          {/* Step Indicators */}
          <div className="max-w-lg mx-auto mb-6">
            <div className="flex justify-between">
              {[1, 2, 3].map((s) => (
                <div key={s} className={`step-${s}`}>
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-8 h-8 flex justify-center items-center rounded-full 
                      ${
                        step === s
                          ? "bg-blue-600 text-white"
                          : step > s
                          ? "bg-blue-500 text-white"
                          : "bg-gray-200 text-gray-600"
                      }`}
                    >
                      {s}
                    </div>
                    <div className="text-sm mt-1">
                      {s === 1 ? "Amount" : s === 2 ? "Review" : "Success"}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Step 1 - Withdraw Form */}
          {step === 1 && (
            <div className="space-y-6">
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">
                    Withdrawal Request
                  </h3>
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Request a withdrawal from your available balance.
                  </p>
                </div>
                <div className="p-6">
                  <form
                    className="space-y-4"
                    onSubmit={(e) => {
                      e.preventDefault();
                      const ok = handleValidated();
                      if (!ok) return;
                      nextStep();
                    }}
                  >
                    {/* Wallet Select */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Withdraw Account
                      </label>
                      <select
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700"
                        value={selectedWallet?.id || ""}
                        onChange={handleSelectWallet}
                      >
                        <option value="">Select a wallet</option>
                        {walletData.map((w) => (
                          <option key={w.id} value={w.id}>
                            {w.name} {w.method_name} {w.currency}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Amount Input */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Amount
                      </label>
                      <input
                        type="number"
                        placeholder="0.00"
                        value={withdrawAmount}
                        onChange={(e) => setWithdrawAmount(e.target.value)}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700"
                      />

                      {selectedWallet && (
                        <p className="text-sm text-red-600 mt-1">
                          Minimum: {selectedWallet?.method.min_withdraw}{" "}
                          {selectedWallet.currency} | Maximum:{" "}
                          {selectedWallet?.method.max_withdraw}{" "}
                          {selectedWallet.currency}
                        </p>
                      )}
                    </div>

                    <button
                      type="submit"
                      className="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"
                    >
                      Request Withdrawal
                    </button>
                  </form>
                </div>
              </div>
            </div>
          )}

          {/* Step 2 - Confirmation */}
          {step === 2 && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  Confirm withdrawal
                </h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  Please review your withdrawal details.
                </p>
              </div>
              <div className="p-6">
                <ul className="flex gap-4 flex-col mb-6 text-gray-700">
                  <li className="flex justify-between">
                    <span>Select Wallet</span>
                    <span>
                      {selectedWallet
                        ? `${selectedWallet.method_name} (${selectedWallet.currency})`
                        : ""}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span>Amount</span>
                    <span>{withdrawAmount}</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Charge</span>
                    <span>{selectedWallet?.method.charge}</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Charge Type</span>
                    <span>
                      {selectedWallet?.method.charge_type}{" "}
                      {selectedWallet?.method?.type &&
                        `(${selectedWallet.method.type})`}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span>Total Amount</span>
                    <span className="text-blue-600 font-semibold">
                      {totalAmount()} {selectedWallet?.currency}
                    </span>
                  </li>
                </ul>

                <div className="flex gap-3">
                  <button
                    onClick={prevStep}
                    className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded w-full"
                  >
                    Back
                  </button>
                  <button
                    onClick={async () => {
                      const ok = handleValidated();
                      if (!ok) return;
                      await handleWithdrawRequest();
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded w-full"
                  >
                    Confirm & Proceed
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Step 3 - Success */}
          {step === 3 && (
            <div className="mx-auto bg-white shadow-lg rounded-lg p-8 text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {transferResponse?.message || "Withdrawal Successful!"}
              </h3>
              <div className="grid grid-cols-12 gap-6 mb-5">
                <div className="col-span-6">
                  <div className="border border-gray-200 py-6 rounded-lg">
                    <p>Transaction ID</p>
                    <p>{transferResponse?.tnx}</p>
                  </div>
                </div>
                <div className="col-span-6">
                  <div className="border border-gray-200 py-6 rounded-lg">
                    <p>Description</p>
                    <p>{transferResponse?.description}</p>
                  </div>
                </div>
                <div className="col-span-6">
                  <div className="border border-gray-200 py-6 rounded-lg">
                    <p>Charge</p>
                    <p>{transferResponse?.charge}</p>
                  </div>
                </div>
                <div className="col-span-6">
                  <div className="border border-gray-200 py-6 rounded-lg">
                    <p>Final Amount</p>
                    <p>{transferResponse?.amount}</p>
                  </div>
                </div>
              </div>
              <button
                onClick={() => {
                  resetSteps();
                  setSelectedWallet(null);
                  setWithdrawAmount("");
                  setTransferResponse(null);
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
              >
                Make Another Withdrawal
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default WithdrawRequest;
